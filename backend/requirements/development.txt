# Code Med Talent - Development Requirements
# Includes base requirements plus development tools

-r base.txt

# Development Tools
django-debug-toolbar==4.2.0
django-extensions==3.2.3
ipython==8.17.2
jupyter==1.0.0

# Code Quality
black==23.11.0
isort==5.12.0
flake8==6.1.0
pylint==3.0.2
mypy==1.7.1
pre-commit==3.5.0

# Testing
pytest==7.4.3
pytest-django==4.7.0
pytest-cov==4.1.0
pytest-mock==3.12.0
factory-boy==3.3.0
faker==20.1.0
coverage==7.3.2

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0

# Database Tools
django-seed==0.3.1

# Performance Profiling
django-silk==5.0.4
memory-profiler==0.61.0

# Environment Management
python-dotenv==1.0.0

# API Testing
httpie==3.2.2
