"""
Code Med Talent - Credentials Models
CredentialChain AI Verification system models for healthcare credentials.
"""

from django.db import models
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _
from django.core.validators import FileExtensionValidator
import uuid

User = get_user_model()


class CredentialType(models.Model):
    """
    Types of healthcare credentials that can be verified.
    """

    class Category(models.TextChoices):
        LICENSE = 'license', _('Professional License')
        CERTIFICATION = 'certification', _('Certification')
        EDUCATION = 'education', _('Education/Degree')
        TRAINING = 'training', _('Training/Course')
        BACKGROUND = 'background', _('Background Check')

    name = models.CharField(
        max_length=100,
        unique=True,
        help_text=_('Name of the credential type (e.g., RN License, BLS Certification)')
    )
    category = models.CharField(
        max_length=20,
        choices=Category.choices,
        help_text=_('Category of credential')
    )
    description = models.TextField(
        blank=True,
        help_text=_('Description of what this credential represents')
    )

    # Verification settings
    requires_verification = models.BooleanField(
        default=True,
        help_text=_('Whether this credential requires third-party verification')
    )
    verification_source = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Primary source for verification (e.g., State Nursing Board)')
    )
    verification_url = models.URLField(
        blank=True,
        help_text=_('URL for verification lookup')
    )

    # Validity settings
    has_expiration = models.BooleanField(
        default=True,
        help_text=_('Whether this credential expires')
    )
    default_validity_months = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text=_('Default validity period in months')
    )

    # Metadata
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'credentials_type'
        verbose_name = _('Credential Type')
        verbose_name_plural = _('Credential Types')
        ordering = ['category', 'name']

    def __str__(self):
        return f"{self.name} ({self.get_category_display()})"


class Credential(models.Model):
    """
    Individual credential instance for a user.
    Part of the CredentialChain system.
    """

    class Status(models.TextChoices):
        PENDING = 'pending', _('Pending Verification')
        VERIFIED = 'verified', _('Verified')
        EXPIRED = 'expired', _('Expired')
        INVALID = 'invalid', _('Invalid/Rejected')
        REVOKED = 'revoked', _('Revoked')

    # Core information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        related_name='credentials'
    )
    credential_type = models.ForeignKey(
        CredentialType,
        on_delete=models.CASCADE,
        related_name='credentials'
    )

    # Credential details
    credential_number = models.CharField(
        max_length=100,
        help_text=_('Official credential/license number')
    )
    issuing_authority = models.CharField(
        max_length=200,
        help_text=_('Organization that issued the credential')
    )
    issuing_state = models.CharField(
        max_length=50,
        blank=True,
        help_text=_('State where credential was issued (if applicable)')
    )

    # Dates
    issue_date = models.DateField(
        help_text=_('Date the credential was issued')
    )
    expiration_date = models.DateField(
        null=True,
        blank=True,
        help_text=_('Date the credential expires')
    )

    # Verification status
    status = models.CharField(
        max_length=20,
        choices=Status.choices,
        default=Status.PENDING
    )
    verification_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text=_('When the credential was last verified')
    )
    verification_source = models.CharField(
        max_length=200,
        blank=True,
        help_text=_('Source used for verification')
    )
    verification_notes = models.TextField(
        blank=True,
        help_text=_('Notes from verification process')
    )

    # Blockchain/Career Passport
    blockchain_hash = models.CharField(
        max_length=128,
        blank=True,
        help_text=_('Blockchain hash for Career Passport')
    )

    # File attachments
    document_file = models.FileField(
        upload_to='credentials/',
        validators=[FileExtensionValidator(['pdf', 'jpg', 'jpeg', 'png'])],
        help_text=_('Scanned copy of the credential')
    )

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'credentials_credential'
        verbose_name = _('Credential')
        verbose_name_plural = _('Credentials')
        unique_together = ['user', 'credential_type', 'credential_number']
        indexes = [
            models.Index(fields=['user', 'status']),
            models.Index(fields=['credential_number']),
            models.Index(fields=['expiration_date']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"{self.credential_type.name} - {self.credential_number}"

    @property
    def is_expired(self):
        """Check if credential is expired."""
        if not self.expiration_date:
            return False
        from django.utils import timezone
        return self.expiration_date < timezone.now().date()

    @property
    def days_until_expiration(self):
        """Calculate days until expiration."""
        if not self.expiration_date:
            return None
        from django.utils import timezone
        delta = self.expiration_date - timezone.now().date()
        return delta.days if delta.days > 0 else 0
