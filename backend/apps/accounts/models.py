"""
Code Med Talent - User Account Models
Custom user model and related account functionality for healthcare talent platform.
"""

from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser):
    """
    Custom User model for Code Med Talent platform.
    Extends Django's AbstractUser with healthcare-specific fields.
    """

    class UserType(models.TextChoices):
        CANDIDATE = 'candidate', _('Healthcare Candidate')
        FACILITY = 'facility', _('Healthcare Facility')
        AGENCY = 'agency', _('Staffing Agency')
        ADMIN = 'admin', _('Platform Admin')

    # Core user information
    email = models.EmailField(_('email address'), unique=True)
    user_type = models.CharField(
        max_length=20,
        choices=UserType.choices,
        default=UserType.CANDIDATE,
        help_text=_('Type of user account')
    )

    # Profile information
    phone_number = models.CharField(
        max_length=20,
        blank=True,
        help_text=_('Primary contact phone number')
    )

    # Account status
    is_verified = models.Bo<PERSON>anField(
        default=False,
        help_text=_('Whether the user has verified their email address')
    )
    is_profile_complete = models.BooleanField(
        default=False,
        help_text=_('Whether the user has completed their profile setup')
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)

    # Use email as the username field
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['username', 'first_name', 'last_name']

    class Meta:
        db_table = 'accounts_user'
        verbose_name = _('User')
        verbose_name_plural = _('Users')
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['user_type']),
            models.Index(fields=['is_active', 'is_verified']),
        ]

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        """Return the user's full name."""
        return f"{self.first_name} {self.last_name}".strip() or self.username

    def get_short_name(self):
        """Return the user's short name."""
        return self.first_name or self.username


class UserProfile(models.Model):
    """
    Extended profile information for users.
    Contains additional fields that may vary by user type.
    """

    user = models.OneToOneField(
        User,
        on_delete=models.CASCADE,
        related_name='profile'
    )

    # Profile details
    bio = models.TextField(
        blank=True,
        max_length=1000,
        help_text=_('Brief biography or description')
    )
    avatar = models.ImageField(
        upload_to='avatars/',
        blank=True,
        null=True,
        help_text=_('Profile picture')
    )

    # Location information
    address_line_1 = models.CharField(max_length=255, blank=True)
    address_line_2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=50, blank=True)
    zip_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, default='United States')

    # Professional information
    linkedin_url = models.URLField(blank=True)
    website_url = models.URLField(blank=True)

    # Preferences
    email_notifications = models.BooleanField(
        default=True,
        help_text=_('Receive email notifications')
    )
    sms_notifications = models.BooleanField(
        default=False,
        help_text=_('Receive SMS notifications')
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounts_user_profile'
        verbose_name = _('User Profile')
        verbose_name_plural = _('User Profiles')

    def __str__(self):
        return f"Profile for {self.user.get_full_name()}"

    @property
    def full_address(self):
        """Return formatted full address."""
        parts = [
            self.address_line_1,
            self.address_line_2,
            f"{self.city}, {self.state} {self.zip_code}".strip(),
            self.country
        ]
        return ', '.join(filter(None, parts))
