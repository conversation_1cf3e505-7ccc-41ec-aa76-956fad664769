{"name": "code-med-talent-frontend", "private": true, "version": "1.0.0", "description": "Code Med Talent - The AI-Powered Talent Ecosystem for Healthcare", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "axios": "^1.6.2", "@reduxjs/toolkit": "^1.9.7", "react-redux": "^8.1.3", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "date-fns": "^2.30.0", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.3.3", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.16", "recharts": "^2.8.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "vite": "^4.5.0", "eslint": "^8.53.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vitest": "^0.34.6", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}