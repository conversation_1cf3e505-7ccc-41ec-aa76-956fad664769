import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { 
  ShieldCheckIcon, 
  UserGroupIcon, 
  ChartBarIcon, 
  LightningBoltIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'

const HomePage = () => {
  const features = [
    {
      name: 'CredentialChain AI Verification',
      description: 'AI cross-references primary sources (Nursing Boards, NCC, AHA) in real-time with blockchain-lite Career Passport technology.',
      icon: ShieldCheckIcon,
      color: 'text-healthcare-600',
      bgColor: 'bg-healthcare-100',
    },
    {
      name: 'CareBot Relationship Nurturing',
      description: 'AI agent builds personalized relationships with passive candidates through empathetic outreach and curated learning opportunities.',
      icon: UserGroupIcon,
      color: 'text-success-600',
      bgColor: 'bg-success-100',
    },
    {
      name: 'CultureFit Predictor',
      description: 'AI-powered facility profiling matches candidates on values alignment, reducing 30-day turnover through longevity-focused matching.',
      icon: ChartBarIcon,
      color: 'text-warning-600',
      bgColor: 'bg-warning-100',
    },
    {
      name: 'RapidResponse Staffing',
      description: 'On-call talent pool for emergency staffing with AI dispatch via geofencing and real-time availability tracking.',
      icon: LightningBoltIcon,
      color: 'text-error-600',
      bgColor: 'bg-error-100',
    },
  ]

  const benefits = [
    'Eliminates 80% of pre-screen labor',
    'Reduces credential fraud risk',
    'Turns candidates into long-term community members',
    'Reduces 30-day turnover significantly',
    'Provides audit-ready compliance reports',
    'Creates defensible competitive moats'
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="healthcare-gradient">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div className="text-center">
              <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                Code Med Talent
              </h1>
              <p className="text-xl md:text-2xl text-healthcare-100 mb-4">
                The AI-Powered Talent Ecosystem for Healthcare
              </p>
              <p className="text-lg text-healthcare-200 mb-8 max-w-3xl mx-auto">
                Where Healthcare Talent Isn't Found—It's Cultivated. 
                Move beyond transactional recruitment to build a self-sustaining talent intelligence network.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/auth/register"
                  className="btn-lg bg-white text-healthcare-600 hover:bg-gray-50 font-semibold"
                >
                  Get Started Today
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </Link>
                <Link
                  to="/auth/login"
                  className="btn-lg border-2 border-white text-white hover:bg-white hover:text-healthcare-600 font-semibold"
                >
                  Sign In
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Problem Statement */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Key Problems We Solve
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Healthcare recruitment faces critical challenges that cost facilities thousands daily
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              { title: 'Credential Chaos', desc: 'Manual verification of licenses/certifications (RN, BLS, state-specific)' },
              { title: 'Passive Candidate Engagement', desc: 'Top talent isn\'t job-hunting; they need nurturing' },
              { title: 'Facility "Fit" Blind Spots', desc: 'Culture mismatch causes high turnover' },
              { title: 'Emergency Staffing', desc: 'Last-minute vacancies cost facilities $10k+/day' },
              { title: 'Compliance Landmines', desc: 'JCAHO, HIPAA, OSHA risks in hiring' },
            ].map((problem, index) => (
              <div key={index} className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{problem.title}</h3>
                <p className="text-gray-600">{problem.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Features Section */}
      <div className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Breakthrough Features
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Revolutionary AI-powered solutions that transform healthcare talent acquisition
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="card p-8">
                <div className="flex items-start">
                  <div className={`flex-shrink-0 p-3 rounded-lg ${feature.bgColor}`}>
                    <feature.icon className={`h-6 w-6 ${feature.color}`} />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">
                      {feature.name}
                    </h3>
                    <p className="text-gray-600">
                      {feature.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Benefits Section */}
      <div className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              Why This is Revolutionary
            </h2>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-start">
                <CheckCircleIcon className="h-6 w-6 text-success-600 flex-shrink-0 mt-1" />
                <span className="ml-3 text-gray-700">{benefit}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="py-16 healthcare-gradient">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Transform Healthcare Recruitment?
          </h2>
          <p className="text-xl text-healthcare-100 mb-8 max-w-2xl mx-auto">
            Join the revolution where healthcare talent isn't found—it's cultivated.
          </p>
          <Link
            to="/auth/register"
            className="btn-lg bg-white text-healthcare-600 hover:bg-gray-50 font-semibold"
          >
            Start Your Free Trial
            <ArrowRightIcon className="ml-2 h-5 w-5" />
          </Link>
        </div>
      </div>
    </div>
  )
}

export default HomePage
