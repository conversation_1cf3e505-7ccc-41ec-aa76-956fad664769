import { configureStore } from '@reduxjs/toolkit'

// Import slice reducers
import authReducer from './slices/authSlice'
import userReducer from './slices/userSlice'
import credentialsReducer from './slices/credentialsSlice'
import candidatesReducer from './slices/candidatesSlice'
import facilitiesReducer from './slices/facilitiesSlice'
import matchingReducer from './slices/matchingSlice'
import notificationsReducer from './slices/notificationsSlice'

// Configure the Redux store
export const store = configureStore({
  reducer: {
    auth: authReducer,
    user: userReducer,
    credentials: credentialsReducer,
    candidates: candidatesReducer,
    facilities: facilitiesReducer,
    matching: matchingReducer,
    notifications: notificationsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: process.env.NODE_ENV !== 'production',
})

export default store
