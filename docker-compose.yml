version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cmt_postgres
    environment:
      POSTGRES_DB: code_med_talent
      POSTGRES_USER: cmt_user
      POSTGRES_PASSWORD: cmt_password_dev
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - cmt_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cmt_user -d code_med_talent"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cmt_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cmt_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Django Backend (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: cmt_backend
    environment:
      - DEBUG=1
      - DATABASE_URL=****************************************************/code_med_talent
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SECRET_KEY=dev-secret-key-change-in-production
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./media:/app/media
      - ./static:/app/static
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cmt_network
    command: >
      sh -c "python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"

  # React Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: cmt_frontend
    environment:
      - REACT_APP_API_URL=http://localhost:8000/api
      - REACT_APP_WS_URL=ws://localhost:8000/ws
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    networks:
      - cmt_network
    command: npm run dev

  # Celery Worker (for background tasks)
  celery:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: cmt_celery
    environment:
      - DEBUG=1
      - DATABASE_URL=****************************************************/code_med_talent
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SECRET_KEY=dev-secret-key-change-in-production
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cmt_network
    command: celery -A config worker -l info

  # Celery Beat (for scheduled tasks)
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: cmt_celery_beat
    environment:
      - DEBUG=1
      - DATABASE_URL=****************************************************/code_med_talent
      - REDIS_URL=redis://redis:6379/0
      - DJANGO_SECRET_KEY=dev-secret-key-change-in-production
    volumes:
      - ./backend:/app
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - cmt_network
    command: celery -A config beat -l info

volumes:
  postgres_data:
  redis_data:

networks:
  cmt_network:
    driver: bridge
