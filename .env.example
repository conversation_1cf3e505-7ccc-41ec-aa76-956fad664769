# Code Med Talent Environment Configuration
# Copy this file to .env and update with your actual values

# Django Settings
DEBUG=True
DJANGO_SECRET_KEY=your-secret-key-here-change-in-production
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# Database Configuration
DATABASE_URL=postgresql://cmt_user:cmt_password_dev@localhost:5432/code_med_talent
DB_NAME=code_med_talent
DB_USER=cmt_user
DB_PASSWORD=cmt_password_dev
DB_HOST=localhost
DB_PORT=5432

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# AWS Configuration (for production)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_STORAGE_BUCKET_NAME=code-med-talent-storage
AWS_S3_REGION_NAME=us-east-1
AWS_S3_CUSTOM_DOMAIN=your-cloudfront-domain.com

# Email Configuration
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Twilio Configuration (for SMS/Voice)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key
OPENAI_ORG_ID=your-openai-org-id

# ElevenLabs Configuration (for AI Voice)
ELEVENLABS_API_KEY=your-elevenlabs-api-key
ELEVENLABS_VOICE_ID=your-default-voice-id

# Hedera Hashgraph Configuration
HEDERA_ACCOUNT_ID=your-hedera-account-id
HEDERA_PRIVATE_KEY=your-hedera-private-key
HEDERA_NETWORK=testnet

# Security & Compliance
ENCRYPTION_KEY=your-encryption-key-for-phi
AUDIT_LOG_LEVEL=INFO
HIPAA_COMPLIANCE_MODE=True

# External APIs
NURSING_LICENSE_API_KEY=your-nursing-license-api-key
BLS_VERIFICATION_API_KEY=your-bls-verification-api-key

# Frontend Configuration
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_WS_URL=ws://localhost:8000/ws
REACT_APP_ENVIRONMENT=development

# Monitoring & Logging
SENTRY_DSN=your-sentry-dsn
LOG_LEVEL=INFO

# File Upload Settings
MAX_UPLOAD_SIZE=********  # 10MB
ALLOWED_FILE_TYPES=pdf,jpg,jpeg,png,doc,docx

# Rate Limiting
RATE_LIMIT_ENABLED=True
RATE_LIMIT_PER_MINUTE=60

# Session Configuration
SESSION_COOKIE_SECURE=False  # Set to True in production
CSRF_COOKIE_SECURE=False     # Set to True in production
SECURE_SSL_REDIRECT=False    # Set to True in production
