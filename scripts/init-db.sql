-- Code Med Talent - Database Initialization Script
-- This script sets up the initial database structure and data

-- Create database if it doesn't exist
SELECT 'CREATE DATABASE code_med_talent'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'code_med_talent')\gexec

-- Connect to the database
\c code_med_talent;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create initial credential types
INSERT INTO credentials_type (name, category, description, requires_verification, verification_source, has_expiration, default_validity_months, is_active, created_at, updated_at) VALUES
('Registered Nurse (RN) License', 'license', 'State-issued nursing license for registered nurses', true, 'State Board of Nursing', true, 24, true, NOW(), NOW()),
('Licensed Practical Nurse (LPN) License', 'license', 'State-issued nursing license for practical nurses', true, 'State Board of Nursing', true, 24, true, NOW(), NOW()),
('Basic Life Support (BLS) Certification', 'certification', 'American Heart Association BLS certification', true, 'American Heart Association', true, 24, true, NOW(), NOW()),
('Advanced Cardiovascular Life Support (ACLS)', 'certification', 'AHA ACLS certification for advanced cardiac care', true, 'American Heart Association', true, 24, true, NOW(), NOW()),
('Pediatric Advanced Life Support (PALS)', 'certification', 'AHA PALS certification for pediatric emergency care', true, 'American Heart Association', true, 24, true, NOW(), NOW()),
('Critical Care Registered Nurse (CCRN)', 'certification', 'AACN certification for critical care nursing', true, 'American Association of Critical-Care Nurses', true, 36, true, NOW(), NOW()),
('Certified Emergency Nurse (CEN)', 'certification', 'Emergency nursing certification', true, 'Board of Certification for Emergency Nursing', true, 48, true, NOW(), NOW()),
('Bachelor of Science in Nursing (BSN)', 'education', 'Four-year nursing degree', true, 'Accredited Nursing School', false, null, true, NOW(), NOW()),
('Associate Degree in Nursing (ADN)', 'education', 'Two-year nursing degree', true, 'Accredited Nursing School', false, null, true, NOW(), NOW()),
('Master of Science in Nursing (MSN)', 'education', 'Graduate nursing degree', true, 'Accredited Nursing School', false, null, true, NOW(), NOW()),
('Nurse Practitioner (NP) Certification', 'certification', 'Advanced practice nursing certification', true, 'National Certification Board', true, 60, true, NOW(), NOW()),
('Certified Nursing Assistant (CNA)', 'certification', 'Basic nursing assistant certification', true, 'State Nursing Assistant Registry', true, 24, true, NOW(), NOW()),
('Medical Assistant Certification', 'certification', 'Certified medical assistant credential', true, 'AAMA or AMT', true, 60, true, NOW(), NOW()),
('Pharmacy Technician Certification', 'certification', 'Certified pharmacy technician credential', true, 'PTCB or ExCPT', true, 24, true, NOW(), NOW()),
('Radiology Technologist License', 'license', 'State license for radiologic technologists', true, 'State Licensing Board', true, 24, true, NOW(), NOW()),
('Physical Therapist License', 'license', 'State license for physical therapists', true, 'State Physical Therapy Board', true, 24, true, NOW(), NOW()),
('Occupational Therapist License', 'license', 'State license for occupational therapists', true, 'State OT Licensing Board', true, 24, true, NOW(), NOW()),
('Respiratory Therapist License', 'license', 'State license for respiratory therapists', true, 'State Respiratory Care Board', true, 24, true, NOW(), NOW()),
('Clinical Laboratory Scientist License', 'license', 'State license for lab scientists', true, 'State Clinical Lab Board', true, 24, true, NOW(), NOW()),
('Background Check', 'background', 'Criminal background verification', true, 'Authorized Background Check Provider', true, 12, true, NOW(), NOW()),
('Drug Screening', 'background', 'Pre-employment drug test', true, 'Certified Testing Facility', true, 12, true, NOW(), NOW()),
('TB Test', 'background', 'Tuberculosis screening test', true, 'Healthcare Provider', true, 12, true, NOW(), NOW()),
('Hepatitis B Vaccination', 'background', 'Hepatitis B immunization record', true, 'Healthcare Provider', false, null, true, NOW(), NOW()),
('Flu Vaccination', 'background', 'Annual influenza vaccination', true, 'Healthcare Provider', true, 12, true, NOW(), NOW()),
('COVID-19 Vaccination', 'background', 'COVID-19 immunization record', true, 'Healthcare Provider', false, null, true, NOW(), NOW())
ON CONFLICT (name) DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_credentials_user_status ON credentials_credential(user_id, status);
CREATE INDEX IF NOT EXISTS idx_credentials_expiration ON credentials_credential(expiration_date) WHERE expiration_date IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_credentials_type_category ON credentials_type(category);
CREATE INDEX IF NOT EXISTS idx_accounts_user_email ON accounts_user(email);
CREATE INDEX IF NOT EXISTS idx_accounts_user_type ON accounts_user(user_type);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns (will be applied when tables are created)
-- These will be created by Django migrations, but we're preparing the function

COMMENT ON DATABASE code_med_talent IS 'Code Med Talent - The AI-Powered Talent Ecosystem for Healthcare';
COMMENT ON EXTENSION "uuid-ossp" IS 'UUID generation functions';
COMMENT ON EXTENSION "pg_trgm" IS 'Trigram matching for text search';

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON DATABASE code_med_talent TO cmt_user;
